import { cookies } from "next/headers";
import { NextResponse } from "next/server";
import { signJwt, verifyJwt } from "@/lib/jwt";

interface JwtPayload {
  id: string;
  email: string;
  iat?: number;
  exp?: number;
}

export async function POST() {
  const cookieStore = await cookies(); // await here for Next.js 15

  const token = cookieStore.get("token")?.value;

  if (!token) {
    return NextResponse.json({ error: "No token" }, { status: 401 });
  }

  try {
    const decoded = verifyJwt(token) as JwtPayload;
    const newToken = signJwt({ id: decoded.id, email: decoded.email });

    const res = NextResponse.json({ message: "Session refreshed" });
    res.cookies.set({
      name: "token",
      value: newToken,
      httpOnly: true,
      path: "/",
      sameSite: "lax",
      secure: process.env.NODE_ENV === "production",
    });
    return res;
  } catch {
    return NextResponse.json({ error: "Invalid token" }, { status: 401 });
  }
}
