{"name": "lovebook-v2", "version": "0.1.0", "private": true, "scripts": {"prepare": "lefthook install", "dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "biome check .", "format": "biome format . --write", "typecheck": "tsc --noEmit", "prisma:generate": "prisma generate", "prisma:migrate:dev": "prisma migrate dev", "prisma:migrate:deploy": "prisma migrate deploy", "prisma:studio": "prisma studio", "prisma:reset": "prisma migrate reset --force", "db:seed": "ts-node prisma/seed.ts"}, "dependencies": {"@heroui/react": "^2.7.11", "@prisma/client": "^6.11.1", "bcrypt": "^6.0.0", "framer-motion": "^12.23.0", "jsonwebtoken": "^9.0.2", "next": "15.3.4", "nodemailer": "^7.0.5", "prisma": "^6.11.1", "react": "^19.0.0", "react-dom": "^19.0.0"}, "devDependencies": {"@arkweid/lefthook": "^0.7.7", "@biomejs/biome": "2.1.1", "@eslint/eslintrc": "^3", "@tailwindcss/typography": "^0.5.16", "@types/jsonwebtoken": "^9.0.10", "@types/node": "^20", "@types/nodemailer": "^6.4.17", "@types/react": "^19", "@types/react-dom": "^19", "autoprefixer": "^10.4.21", "eslint-config-next": "15.3.4", "postcss": "^8.5.6", "tailwindcss": "^3.4.17", "ts-node": "^10.9.2", "typescript": "^5"}}